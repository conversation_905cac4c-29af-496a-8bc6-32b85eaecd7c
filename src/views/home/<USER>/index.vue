<template>
  <div class="chat-input-box">
    <div
      class="input-container"
      :class="{ 'drag-over': isDragOver }"
      @drop.prevent="handleDrop"
      @dragover.prevent="handleDragOver"
      @dragenter.prevent="handleDragEnter"
      @dragleave.prevent="handleDragLeave"
    >
      <!-- 拖拽提示层 -->
      <div v-show="isDragOver" class="drag-overlay">
        <div class="drag-content">
          <i class="el-icon-upload2"></i>
          <p>释放文件以上传</p>
        </div>
      </div>

      <!-- 主输入区域 -->
      <div class="input-wrapper">
        <el-input
          v-model="inputMessage"
          type="textarea"
          :placeholder="placeholder"
          :autosize="{ minRows: 1, maxRows: 6 }"
          resize="none"
          class="message-input"
          @keyup.enter.native="handleEnter"
          @paste.native="handlePaste"
          ref="messageInput"
        />
        
        <!-- 工具栏 -->
        <div class="toolbar">
          <!-- 左侧工具 -->
          <div class="toolbar-left">
            <!-- 深度思考按钮 -->
            <el-button
              type="text"
              class="tool-btn research-btn"
              @click="toggleResearch"
              :class="{ active: isResearchMode }"
            >
              <i class="el-icon-search"></i>
              深度思考
            </el-button>
            <!-- 联网搜索按钮 -->
            <el-button
              type="text"
              class="tool-btn search-btn"
              @click="toggleSearch"
              :class="{ active: isSearchMode }"
              :disabled="uploadedFiles.length > 0"
            >
              <i class="el-icon-connection"></i>
              联网搜索
            </el-button>
          </div>
          
          <!-- 右侧工具 -->
          <div class="toolbar-right">
            <!-- 文件上传 -->
            <el-tooltip
              effect="dark"
              placement="top"
              :content="fileUploadTooltip"
              :open-delay="300"
            >
              <el-upload
                ref="fileUpload"
                :show-file-list="false"
                :before-upload="handleFileUpload"
                :accept="acceptedFileTypes"
                action=""
                class="file-upload"
              >
                <el-button type="text" class="tool-btn upload-btn">
                  <i class="el-icon-folder-add"></i>
                  <span class="upload-text"></span>
                </el-button>
              </el-upload>
            </el-tooltip>
            
            <!-- 语音输入 -->
            <!-- <el-button
              type="text"
              class="tool-btn voice-btn"
              @click="toggleVoiceInput"
              :class="{ active: isVoiceMode }"
            >
              <i class="el-icon-microphone"></i>
            </el-button> -->
            
            <!-- 发送按钮 -->
            <el-button
              type="primary"
              class="send-btn"
              @click="sendMessage"
              :disabled="!canSend"
              circle
            >
              <i class="el-icon-top-right"></i>
            </el-button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 上传的文件预览 -->
    <div v-if="uploadedFiles.length > 0" class="uploaded-files">
      <div
        v-for="(file, index) in uploadedFiles"
        :key="index"
        class="file-item"
      >
        <div class="file-info">
          <i class="el-icon-document"></i>
          <span class="file-name">{{ file.name }}</span>
        </div>
        <el-button
          type="text"
          class="remove-file"
          @click="removeFile(index)"
        >
          <i class="el-icon-close"></i>
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "ChatInputBox",
  props: {
    placeholder: {
      type: String,
      default: "发消息、拖拽或粘贴文件..."
    },
    acceptedFileTypes: {
      type: String,
      default: ".pdf,.docx,.txt,.pptx,.xlsx,.csv,.md,.jpg,.jpeg,.png"
    },
    maxFileCount: { // 限制文件上传数量
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      inputMessage: "",
      isResearchMode: false,
      isSearchMode: false,
      isVoiceMode: false,
      uploadedFiles: [],
      fileObjects: [],
      isDragOver: false,
    };
  },
  computed: {
    canSend() {
      return this.inputMessage.trim().length > 0 || this.uploadedFiles.length > 0;
    },
    fileUploadTooltip() {
      const types = this.acceptedFileTypes.split(',').map(type => type.trim());
      const formatGroups = {
        '文档': ['.pdf', '.docx', '.txt', '.md'],
        '表格': ['.xlsx', '.csv'],
        '演示': ['.pptx'],
        '图片': ['.jpg', '.jpeg', '.png']
      };

      let tooltip = '支持的文件格式：\n';

      Object.keys(formatGroups).forEach(category => {
        const categoryTypes = formatGroups[category].filter(type => types.includes(type));
        if (categoryTypes.length > 0) {
          tooltip += `${category}：${categoryTypes.join(', ')}\n`;
        }
      });

      tooltip += '\n文件大小限制：15MB';
      return tooltip;
    }
  },
  methods: {
    handleEnter(event) {
      if (!event.shiftKey) {
        event.preventDefault();
        this.sendMessage();
      }
    },

    handlePaste(event) {
      // 检查剪贴板中是否有文件
      const clipboardData = event.clipboardData;
      if (!clipboardData) return;

      const items = clipboardData.items;
      if (!items) return;

      // 遍历剪贴板项目，查找文件
      for (let i = 0; i < items.length; i++) {
        const item = items[i];

        // 如果是文件类型
        if (item.kind === 'file') {
          event.preventDefault(); // 阻止默认粘贴行为

          const file = item.getAsFile();
          if (file) {
            this.processFile(file);
          }
        }
      }
    },
    
    sendMessage() {
      if (!this.canSend) return;
      
      // 是否开启联网搜索模式
      let isSearch = ""
      if (this.isSearchMode) {
        isSearch = "<online search> "
      }

      // 是否开启思考模式
      let isThink = "/no_think"
      if (this.isResearchMode) {
        isThink = ""
      }
      
      const messageData = {
        type: 'dify-chatbot-send-message',
        payload: {
            message: this.inputMessage.trim() + "<span style='display: none;'>" + isSearch + isThink + "</span>",
            files: this.uploadedFiles, 
            fileObjects: this.fileObjects,
        }
      };
      console.log("messageData:", messageData)
      
      this.$emit("send-message", messageData);
      
      // 清空输入
      this.inputMessage = "";
      this.uploadedFiles = [];
      this.fileObjects = [];
      this.isSearchMode = false;
      this.isResearchMode = false;
      
      // 重新聚焦输入框
      this.$nextTick(() => {
        this.$refs.messageInput.focus();
      });
    },
    
    toggleSearch() {
      // 如果有文件上传，不允许开启联网搜索
      if (this.uploadedFiles.length > 0) {
        this.$message.warning('上传文件时不支持联网搜索');
        return;
      }
      this.isSearchMode = !this.isSearchMode;
    },

    toggleResearch() {
      this.isResearchMode = !this.isResearchMode;
    },
    
    toggleVoiceInput() {
      this.isVoiceMode = !this.isVoiceMode;
      // 这里可以添加语音输入的逻辑
      if (this.isVoiceMode) {
        this.startVoiceInput();
      } else {
        this.stopVoiceInput();
      }
    },
    
    handleFileUpload(file) {
      // 检查文件数量限制
      if (this.uploadedFiles.length >= this.maxFileCount) {
        this.$message.warning(`最多只能上传 ${this.maxFileCount} 个文件`);
        return false;
      }

      // 检查文件大小（限制为15MB）
      const maxSize = 15 * 1024 * 1024;
      if (file.size > maxSize) {
        this.$message.error(`文件 ${file.name} 大小不能超过15MB`);
        return false;
      }

      // 上传文件时关闭联网搜索模式
      if (this.isSearchMode) {
        this.isSearchMode = false;
      }

      // 添加到上传文件列表
      this.uploadedFiles.push(file);
      
      // this.$emit("upload-file", file);
      try {
        this.$emit("upload-file", file, (response) => {
          // 这里可以处理成功回调
          // console.log("上传成功:", response);
          const fileObject = {
            id: response.id,
            name: response.name,
            size: response.size,
            type: response.mime_type,
            uploadedId: response.id,
          }
          this.fileObjects.push(fileObject);
        });
      } catch (error) {
        // console.error(file.name, "上传失败:", error);
      }

      return false; // 阻止默认上传行为
    },
    
    removeFile(index) {
      // 同步删除uploadedFiles和fileObjects中对应的文件
      this.uploadedFiles.splice(index, 1);
      this.fileObjects.splice(index, 1);
    },
    
    startVoiceInput() {
      // 语音输入逻辑
      console.log("开始语音输入");
    },
    
    stopVoiceInput() {
      // 停止语音输入逻辑
      console.log("停止语音输入");
    },

    // 拖拽上传相关方法
    handleDragEnter() {
      this.isDragOver = true;
    },

    handleDragOver() {
      // 已在模板中使用 .prevent 修饰符
    },

    handleDragLeave(event) {
      // 只有当离开整个容器时才隐藏拖拽提示
      if (!event.currentTarget.contains(event.relatedTarget)) {
        this.isDragOver = false;
      }
    },

    handleDrop(event) {
      this.isDragOver = false;
      const files = Array.from(event.dataTransfer.files);

      if (files.length === 0) return;

      // 处理拖拽的文件
      files.forEach(file => {
        this.processFile(file);
      });
    },

    // 用于拖拽和粘贴文件
    processFile(file) {
      // 检查文件数量限制
      if (this.uploadedFiles.length >= this.maxFileCount) {
        this.$message.warning(`最多只能上传 ${this.maxFileCount} 个文件`);
        return false;
      }

      // 上传文件时关闭联网搜索模式
      if (this.isSearchMode) {
        this.isSearchMode = false;
      }

      // 检查文件类型
      const acceptedTypes = this.acceptedFileTypes.split(',').map(type => type.trim());
      const fileExtension = '.' + file.name.split('.').pop().toLowerCase();

      if (!acceptedTypes.includes(fileExtension)) {
        this.$message.error(`${file.name}, 不支持的文件类型: ${fileExtension}`);
        return;
      }

      // 检查文件大小
      const maxSize = 15 * 1024 * 1024; // 10MB
      if (file.size > maxSize) {
        this.$message.error(`文件 ${file.name} 大小不能超过15MB`);
        return;
      }

      // 检查重复文件
      const isDuplicate = this.uploadedFiles.some(uploadedFile =>
        uploadedFile.name === file.name && uploadedFile.size === file.size
      );

      if (isDuplicate) {
        this.$message.warning(`文件 "${file.name}" 已经添加过了`);
        return;
      }

      // 添加到文件列表
      this.uploadedFiles.push(file);
      // this.$message.success(`文件 "${file.name}" 添加成功`);

      // 触发上传事件
      this.$emit("upload-file", file, (response) => {
        // console.log("拖拽上传成功:", response);
        const fileObject = {
          id: response.id,
          name: response.name,
          size: response.size,
          type: response.mime_type,
          uploadedId: response.id,
        };
        this.fileObjects.push(fileObject);
      });
    }
  },
  
  mounted() {
    // 自动聚焦输入框
    this.$nextTick(() => {
      this.$refs.messageInput.focus();
    });
  }
};
</script>

<style scoped lang="scss">
.chat-input-box {
  width: 100%;
  max-width: 800px;
}

.input-container {
  background: #ffffff;
  border: 1px solid rgba(100, 181, 246, 0.2);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(100, 181, 246, 0.1);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;

  &:hover {
    border-color: rgba(100, 181, 246, 0.5);
    box-shadow: 0 12px 40px rgba(100, 181, 246, 0.15);
    transform: translateY(-2px);
  }

  &:focus-within {
    border-color: rgba(100, 181, 246, 0.8);
    box-shadow: 0 16px 48px rgba(100, 181, 246, 0.2);
    transform: translateY(-4px);
  }

  &.drag-over {
    border-color: rgba(100, 181, 246, 0.8);
    background: rgba(100, 181, 246, 0.05);
    box-shadow: 0 16px 48px rgba(100, 181, 246, 0.3);
    transform: translateY(-4px) scale(1.02);
  }
}

.drag-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(100, 181, 246, 0.1);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  backdrop-filter: blur(4px);
  border: 2px dashed rgba(100, 181, 246, 0.6);
}

.drag-content {
  text-align: center;
  color: #1976d2;

  i {
    font-size: 48px;
    margin-bottom: 12px;
    display: block;
    animation: dragBounce 1s ease-in-out infinite;
  }

  p {
    font-size: 16px;
    font-weight: 500;
    margin: 0;
  }
}

@keyframes dragBounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8px);
  }
}

.input-wrapper {
  position: relative;
  padding: 16px;
}

.message-input {
  ::v-deep .el-textarea__inner {
    border: none;
    padding: 0;
    font-size: 16px;
    line-height: 1.5;
    resize: none;
    box-shadow: none;
    background: transparent;
    
    &:focus {
      border: none;
      box-shadow: none;
    }
    
    &::placeholder {
      color: #a8abb2;
      font-size: 16px;
    }
  }
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tool-btn {
  padding: 8px 12px;
  font-size: 14px;
  color: #606266;
  border: none;
  background: transparent;
  border-radius: 6px;
  transition: all 0.2s ease;
  
  &:hover {
    background: #f5f7fa;
    color: #409eff;
  }
  
  &.active {
    background: #ecf5ff;
    color: #409eff;
  }
  
  i {
    margin-right: 4px;
  }
}

.research-btn {
  border: 1px solid rgba(100, 181, 246, 0.3);
  transition: border-color 0.2s ease;
  
  &:hover {
    border-color: rgba(100, 181, 246, 0.6);
  }
  
  &.active {
    background: #e6f7ff;
    color: #1890ff;
    border-color: rgba(100, 181, 246, 0.8);
  }
}

.search-btn {
  border: 1px solid rgba(100, 181, 246, 0.3);
  transition: border-color 0.2s ease;

  &:hover:not(:disabled) {
    border-color: rgba(100, 181, 246, 0.6);
  }

  &.active {
    background: #e6f7ff;
    color: #1890ff;
    border-color: rgba(100, 181, 246, 0.8);
  }

  &:disabled {
    background: #f5f5f5;
    color: #c0c4cc;
    border-color: #e4e7ed;
    cursor: not-allowed;

    &:hover {
      background: #f5f5f5;
      color: #c0c4cc;
      border-color: #e4e7ed;
    }
  }
}

.upload-btn {
  padding: 8px 12px;
  height: 36px;
  background: rgba(100, 181, 246, 0.1);
  border: 1px solid rgba(100, 181, 246, 0.3);
  border-radius: 8px;
  color: #1976d2;
  transition: all 0.3s ease;

  &:hover {
    background: rgba(100, 181, 246, 0.2);
    border-color: rgba(100, 181, 246, 0.6);
    color: #1565c0;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(100, 181, 246, 0.2);
  }

  i {
    margin-right: 4px;
    font-size: 18px;
  }

  .upload-text {
    font-size: 14px;
    font-weight: 500;
  }
}

.voice-btn {
  padding: 8px;
  width: 36px;
  height: 36px;

  i {
    margin: 0;
    font-size: 16px;
  }
}

.send-btn {
  width: 40px;
  height: 40px;
  padding: 0;
  background: linear-gradient(135deg, #64b5f6 0%, #42a5f5 100%);
  border: none;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 4px 12px rgba(100, 181, 246, 0.3);

  &:hover {
    background: linear-gradient(135deg, #42a5f5 0%, #2196f3 100%);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 8px 20px rgba(100, 181, 246, 0.4);
  }

  &:active {
    transform: translateY(0) scale(1);
    box-shadow: 0 2px 8px rgba(100, 181, 246, 0.3);
  }

  &:disabled {
    background: linear-gradient(135deg, #c0c4cc 0%, #a8a8a8 100%);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }

  i {
    font-size: 18px;
    color: white;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
  }
}

.file-upload {
  ::v-deep .el-upload {
    display: inline-block;
  }
}

.uploaded-files {
  margin-top: 12px;
  padding: 0 16px 16px;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 8px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
  
  i {
    color: #909399;
    margin-right: 8px;
  }
}

.file-name {
  font-size: 14px;
  color: #606266;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.remove-file {
  padding: 4px;
  color: #909399;
  
  &:hover {
    color: #f56c6c;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .input-wrapper {
    padding: 12px;
  }
  
  .message-input {
    ::v-deep .el-textarea__inner {
      font-size: 14px;
    }
  }
  
  .toolbar {
    margin-top: 8px;
    padding-top: 8px;
  }
  
  .tool-btn {
    padding: 6px 8px;
    font-size: 12px;
  }
  
  .research-btn {
    i {
      margin-right: 2px;
    }
  }

  .search-btn {
    i {
      margin-right: 2px;
    }
  }

  .upload-btn {
    padding: 6px 10px;

    i {
      font-size: 16px;
      margin-right: 3px;
    }

    .upload-text {
      font-size: 12px;
    }
  }
}
</style>
